
<div x-data="typewriter({
  textList: {{ text_list|safe }},
  speed: {{ speed }},
  cursor: '{{ cursor|escapejs }}',
  loop: {{ loop|lower }},
  deleteSpeed: {{ delete_speed }},
  delay: {{ delay }},
  className: '{{ class_name|escapejs }}'
})">
  <span :class="className">
    <span x-text="displayText"></span>
    <span class="animate-pulse" x-text="cursor"></span>
  </span>
</div>

<script>
document.addEventListener('alpine:init', () => {
  Alpine.data('typewriter', (config) => ({
    displayText: '',
    currentIndex: 0,
    textArrayIndex: 0,
    isDeleting: false,
    timeoutId: null,
    
    // Configuration with defaults
    textList: config.textList || [''],
    speed: config.speed || 100,
    cursor: config.cursor || '|',
    loop: config.loop || false,
    deleteSpeed: config.deleteSpeed || 50,
    delay: config.delay || 1500,
    className: config.className || '',

    init() {
      this.startTyping();
    },
    
    get currentText() {
      return this.textList[this.textArrayIndex] || '';
    },
    
    startTyping() {
      this.timeoutId = setTimeout(() => {
        if (!this.currentText) return;
        
        if (!this.isDeleting) {
          // Typing phase
          if (this.currentIndex < this.currentText.length) {
            this.displayText = this.currentText.substring(0, this.currentIndex + 1);
            this.currentIndex++;
          } else if (this.loop) {
            // Pause before deleting
            this.isDeleting = true;
            this.timeoutId = setTimeout(() => {
              this.startTyping();
            }, this.delay);
            return;
          }
        } else {
          // Deleting phase
          if (this.displayText.length > 0) {
            this.displayText = this.displayText.slice(0, -1);
          } else {
            // Switch to next text item
            this.isDeleting = false;
            this.currentIndex = 0;
            this.textArrayIndex = (this.textArrayIndex + 1) % this.textList.length;
          }
        }
        
        this.startTyping();
      }, this.isDeleting ? this.deleteSpeed : this.speed);
    },
    
    // Clean up timeouts when component is removed
    destroy() {
      clearTimeout(this.timeoutId);
    }
  }));
});
</script>