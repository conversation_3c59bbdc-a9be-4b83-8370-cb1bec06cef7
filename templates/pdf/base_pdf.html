<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>{{ lesson.selected_title }}</title>
    <style>
      @page {
        size: A4;
        margin: 1.5cm;

        @bottom-center {
          content: "This lesson was created by sagesheets.pro";
          font-size: 10px;
          color: #666;
          border-top: 1px solid #eee;
          padding-top: 5px;
          width: 100%;
        }
      }
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        counter-reset: section;
      }
      .lesson-header {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #3498db;
      }
      .module-section {
        margin-bottom: 2rem;
        page-break-inside: avoid;
      }
      .module-title {
        font-size: 1.3rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 0.8rem;
        padding-bottom: 0.3rem;
        border-bottom: 1px solid #ecf0f1;
      }
      .vocab-section {
        margin-bottom: 1.2rem;
      }
      .vocab-title {
        font-weight: bold;
        color: #16a085;
      }
      ol.questions {
        padding-left: 1.5rem;
      }
      ol.questions li {
        margin-bottom: 0.8rem;
      }
    </style>
  </head>
  <body>
    <header class="lesson-header">
      <h1>{{ lesson.selected_title }}</h1>
      <p>
        <strong>Level:</strong> {{ lesson.level }} |
        <strong>Language:</strong> {{ lesson.language.name }} |
        <strong>Style:</strong> {{ lesson.style }}
      </p>
    </header>

    <main>
      {% for module in lesson.modules %}
        <section class="module-section">
          {% include module.template %}
        </section>
      {% endfor %}
    </main>
  </body>
</html>