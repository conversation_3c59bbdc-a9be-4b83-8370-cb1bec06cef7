# ==============================================================================
# Example .env file
# Please copy this file to `.env` and substitute all the values required.
# Some values are prefilled, some you will need to get from another
# developer, and some will be specific to your local setup.
# ==============================================================================


# ==============================================================================
# Base Settings
# ==============================================================================
DEBUG=True
SECRET_KEY=xxx # generate from https://djecrety.ir/
SLACK_BOT_TOKEN=xxx
DEFAULT_SLACK_CHANNEL=xxx
GEMINI_API_KEY=xxx

# ==============================================================================
# Local Database Settings
# These are the same values from the docker-compose.yml file
# ==============================================================================
DB_NAME=django
DB_USER=django
DB_PASS=django
DB_HOST=db

# ==============================================================================
# Task Manager Settings
# ==============================================================================
REDIS_URL=redis://redis:6379/0

# ==============================================================================
# Version
# ==============================================================================
VERSION=

# ==============================================================================
# Sentry Settings
# ==============================================================================

ENABLE_SENTRY=
SENTRY_ENV=
SENTRY_DSN=

# ==============================================================================
# Stripe Settings
# ==============================================================================
# Get the webhook secret by looking at the console output of the stripe-cli container.
# These values are used by the web container and stripe-cli container.

STRIPE_WEBHOOK_SECRET=whsec_xxxx
STRIPE_DEVICE_NAME=Local <Your Name>
STRIPE_PK_KEY=sk_test_xxxx
STRIPE_SK_KEY=pk_test_xxxx

# ==============================================================================
# Google Captcha Credentials
# ==============================================================================
# The credentials below are provided by Google for testing purposes.
# It is not a security risk to place them here.

RECAPTCHA_PRIVATE_KEY=6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe
RECAPTCHA_PUBLIC_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI

# ==============================================================================
# Google SSO
# To get these credentials you'll need to create an app in google developer console
# and get the credentials. Please check the README
# ==============================================================================
DJANGO_ADMIN_SSO_OAUTH_CLIENT_ID=xxx
DJANGO_ADMIN_SSO_OAUTH_CLIENT_SECRET=xxx

# ==============================================================================
# Github Secret Credentials
# These will be needed to do Oauth login with github
# ==============================================================================
GITHUB_CLIENT_ID=xxx
GITHUB_SECRET_KEY=xxx


# ==============================================================================
# MAILGUN Variables
# ==============================================================================
MAILGUN_API_KEY=xxx
MAILGUN_SENDER_DOMAIN=xxx

# ==============================================================================
# Database Settings
# ==============================================================================
# These values are used by the web container and db container.
# These should not be used as places for the those servers to hold the data,
# just purely for the management commands to run.

TEST_DB_NAME=xxx
TEST_DB_USER=xxx
TEST_DB_PASSWORD=xxx
TEST_DB_HOST=xxx

DEV_DB_NAME=xxx
DEV_DB_USER=xxx
DEV_DB_PASSWORD=xxx
DEV_DB_HOST=xxx

PROD_DB_NAME=xxx
PROD_DB_USER=xxx
PROD_DB_PASSWORD=xxx
PROD_DB_HOST=xxx

