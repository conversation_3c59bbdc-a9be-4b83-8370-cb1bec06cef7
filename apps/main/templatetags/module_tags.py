import json
from django import template
from django.core.cache import cache

from apps.main.models import FAQ

register = template.Library()


@register.inclusion_tag("components/modules/faqs.html")
def faqs_module():
    """
    Grab social media links from the database and return them to the template.
    :return: A dictionary containing the social media links.
    """
    faqs = cache.get("frequently_asked_questions")
    if faqs is None:
        faqs = list(FAQ.objects.filter(module=True))
        cache.set("frequently_asked_questions", faqs, 3600)
    return {"faqs": faqs}

@register.inclusion_tag('components/modules/typewriter.html')
def typewriter(text_list, speed=100, cursor="|", loop=True, delete_speed=50, delay=300, class_name=''):
    # Handle both string and list inputs
    if isinstance(text_list, str):
        try:
            # Try to parse as JSON list if passed as string
            text_list = json.loads(text_list.replace("'", '"'))
        except json.JSONDecodeError:
            # Fallback to single item list
            text_list = [text_list]
    
    return {
        'text_list': text_list,
        'speed': speed,
        'cursor': cursor,
        'loop': loop,
        'delete_speed': delete_speed,
        'delay': delay,
        'class_name': class_name,
    }