from django.contrib import admin
from django.contrib.admin import widgets
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.forms import Textarea
from django.utils.html import format_html
import json

from .models import Language, Lesson, Discussion, Reading, Vocabulary


@admin.register(Language)
class LanguageAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "code",
    )
    search_fields = ("name", "code")
    list_per_page = 20


class DiscussionInline(admin.StackedInline):
    model = Discussion
    extra = 0
    readonly_fields = ("questions_preview",)
    fields = ("questions_preview",)

    def questions_preview(self, instance):
        if instance.questions:
            return format_html(
                "<ul>{}</ul>",
                "".join(
                    f"<li>{q}</li>" for q in instance.questions.get("questions", [])
                ),
            )
        return "-"

    questions_preview.short_description = "Questions Preview"


class LessonAdmin(admin.ModelAdmin):
    list_display = ("selected_title", "language", "level", "created")
    list_filter = ("level", "language")
    search_fields = ("topic", "selected_title", "keywords")
    autocomplete_fields = ("language",)
    readonly_fields = ("generate_titles_action",)
    actions = ["generate_discussions", "create_vocabulary"]
    inlines = [DiscussionInline]

    fieldsets = (
        (None, {"fields": ("language", "level", "topic", "keywords")}),
        (
            "Generated Content",
            {
                "fields": ("selected_title", "generate_titles_action"),
            },
        ),
    )

    def generate_titles_action(self, instance):
        return format_html(
            '<a class="button" href="generate-titles/">Generate Titles</a>'
        )

    generate_titles_action.short_description = "Title Generation"

    @admin.action(description="Generate Discussions for selected Lessons")
    def generate_discussions(self, request, queryset):
        for lesson in queryset:
            lesson.generate_discussion()

    @admin.action(description="Create Vocabulary entries")
    def create_vocabulary(self, request, queryset):
        for lesson in queryset:
            Vocabulary.objects.get_or_create(lesson=lesson)


class DiscussionAdmin(admin.ModelAdmin):
    list_display = ("lesson_title", "questions_count", "created")
    list_select_related = ("lesson",)

    def lesson_title(self, obj):
        return obj.lesson.selected_title

    lesson_title.short_description = "Lesson Title"

    def questions_count(self, obj):
        return len(obj.questions.get("questions", [])) if obj.questions else 0

    questions_count.short_description = "# Questions"


class ReadingAdmin(admin.ModelAdmin):
    list_display = ("lesson_title", "text_preview")
    list_select_related = ("lesson",)
    search_fields = ("lesson__selected_title", "text")

    def lesson_title(self, obj):
        return obj.lesson.selected_title

    lesson_title.short_description = "Lesson Title"

    def text_preview(self, obj):
        return f"{obj.text[:100]}..." if len(obj.text) > 100 else obj.text

    text_preview.short_description = "Text Preview"


class VocabularyAdmin(admin.ModelAdmin):
    list_display = ("lesson_title", "words_count", "sentences_count")
    list_select_related = ("lesson",)
    formfield_overrides = {
        JSONField: {
            "widget": widgets.AdminTextareaWidget(attrs={"rows": 4, "cols": 100})
        },
    }

    def lesson_title(self, obj):
        return obj.lesson.selected_title

    lesson_title.short_description = "Lesson Title"

    def words_count(self, obj):
        return len(obj.words.get("words", [])) if obj.words else 0

    words_count.short_description = "# Words"

    def sentences_count(self, obj):
        return len(obj.sentences.get("sentences", [])) if obj.sentences else 0

    sentences_count.short_description = "# Sentences"


admin.site.register(Lesson, LessonAdmin)
admin.site.register(Discussion, DiscussionAdmin)
admin.site.register(Reading, ReadingAdmin)
admin.site.register(Vocabulary, VocabularyAdmin)
