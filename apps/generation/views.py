from django.shortcuts import render, get_object_or_404
from apps.generation.models import Lesson




def generate_lesson_titles(request, lesson_id):
    """htmx view to return a list of titles made by the AI"""
    lesson = get_object_or_404(Lesson, pk=lesson_id)
    titles = lesson.generate_titles()

    context = {
        "titles": titles,
    }

    return render(request, "partials/generate_lesson_titles.html", context)
